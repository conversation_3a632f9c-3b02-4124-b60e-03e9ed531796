"""
Stage 6: Test Script Generation for GretahAI ScriptWeaver

This module handles the test script generation phase of the application workflow.
It provides functionality for:
- Generating test scripts for selected test case steps using Google AI
- Two-phase script generation process (isolated script + merging with previous steps)
- Script file management with unique timestamped filenames
- Multiple script views (merged, step-specific, diff, origins)
- Integration with test data and element matches from previous stages
- Proper workflow transitions to Stage 7 (Run Script)

The stage supports both merged script generation that maintains continuity with
previous steps and step-specific script generation for isolated testing.
All Google AI API calls are routed through the centralized generate_llm_response
function in core/ai.py for consistent logging and error handling.

Functions:
    stage6_generate_script(state): Main Stage 6 function for test script generation
"""

import os
import logging
import streamlit as st
import time
from pathlib import Path
from datetime import datetime

# Import helper functions from other modules
from core.helpers_diff import generate_html_diff, analyze_script_origins, generate_colored_html_script

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage6")


def create_final_step_script(state):
    """
    Create a script file containing only the final step for the current test case.

    This function creates a script with only the last step's implementation, which is
    what should be optimized in Stage 8. The final step script already contains all
    the necessary context and continuity from previous steps.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        str: Path to the final step script file, or None if creation failed
    """
    try:
        # Check if we have a test case and previous scripts
        if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
            logger.warning("No selected test case found, cannot create final step script")
            return None

        if not hasattr(state, 'previous_scripts') or not state.previous_scripts:
            logger.warning("No previous scripts found, cannot create final step script")
            return None

        # Get the test case ID
        test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')

        # Get all step numbers and find the highest (final) step
        step_numbers = sorted(state.previous_scripts.keys(), key=int)

        if not step_numbers:
            logger.warning("No step scripts found, cannot create final step script")
            return None

        # Get the final step number and its script
        final_step_no = step_numbers[-1]  # Last step in sorted order
        final_step_script = state.previous_scripts[final_step_no]

        logger.info(f"Stage 6: Creating final step script for test case {test_case_id}, final step: {final_step_no}")

        # Add a header comment to the final step script
        header = f"""# Final Step Script for Test Case: {test_case_id}
# Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Final step: {final_step_no} (contains all previous step context)
# Created in Stage 6 (Script Generation) for Stage 8 optimization
"""
        final_script_content = header + final_step_script

        # Create a file path for the final step script
        script_dir = "generated_tests"
        os.makedirs(script_dir, exist_ok=True)
        final_script_file = os.path.join(
            script_dir,
            f"test_{test_case_id}_final_step_{final_step_no}_{int(time.time())}.py"
        )

        # Save the final step script to a file
        with open(final_script_file, "w") as f:
            f.write(final_script_content)

        # Store the final step script content and path in the state for Stage 8
        state.combined_script_content = final_script_content
        state.combined_script_path = final_script_file
        logger.info(f"Stage 6: Stored final step script content in state for Stage 8 (length: {len(final_script_content)} chars)")
        logger.info(f"Stage 6: Stored final step script path in state: {final_script_file}")

        logger.info(f"Stage 6: Successfully created final step script file: {final_script_file}")
        return final_script_file

    except Exception as e:
        logger.error(f"Stage 6: Error creating final step script: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def create_combined_script(state):
    """
    Create a combined script file containing all steps for the current test case.

    This function is called from Stage 6 when script generation is complete for all steps.
    It merges all individual step scripts in the correct order using simple concatenation.

    NOTE: For Stage 8 optimization, use create_final_step_script() instead, which only
    includes the final step's script (as the final step already contains all context).

    Args:
        state (StateManager): The application state manager instance

    Returns:
        str: Path to the combined script file, or None if creation failed
    """
    try:
        # Check if we have a test case and previous scripts
        if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
            logger.warning("No selected test case found, cannot create combined script")
            return None

        if not hasattr(state, 'previous_scripts') or not state.previous_scripts:
            logger.warning("No previous scripts found, cannot create combined script")
            return None

        # Get the test case ID
        test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')

        # Get all step numbers in order
        step_numbers = sorted(state.previous_scripts.keys(), key=int)

        if not step_numbers:
            logger.warning("No step scripts found, cannot create combined script")
            return None

        logger.info(f"Stage 6: Creating combined script for test case {test_case_id} with steps: {', '.join(step_numbers)}")

        # Start with the first step's script
        combined_script = state.previous_scripts[step_numbers[0]]

        # Add a header comment to the combined script
        header = f"""# Combined Test Script for Test Case: {test_case_id}
# Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Includes steps: {', '.join(step_numbers)}
# Created in Stage 6 (Script Generation)
"""
        combined_script = header + combined_script

        # Concatenate each subsequent step's script (simple approach for Stage 6)
        for i in range(1, len(step_numbers)):
            current_step = step_numbers[i]
            current_script = state.previous_scripts[current_step]

            # Use simple concatenation in Stage 6 (AI optimization happens in Stage 8)
            logger.info(f"Stage 6: Concatenating step {current_step} into combined script")
            combined_script += f"\n\n# --- STEP {current_step} ---\n{current_script}"

        # Create a file path for the combined script
        script_dir = "generated_tests"
        os.makedirs(script_dir, exist_ok=True)
        combined_script_file = os.path.join(
            script_dir,
            f"test_{test_case_id}_combined_{int(time.time())}.py"
        )

        # Save the combined script to a file
        with open(combined_script_file, "w") as f:
            f.write(combined_script)

        # Store the combined script content and path in the state for Stage 8
        state.combined_script_content = combined_script
        state.combined_script_path = combined_script_file
        logger.info(f"Stage 6: Stored combined script content in state for Stage 8 (length: {len(combined_script)} chars)")
        logger.info(f"Stage 6: Stored combined script path in state: {combined_script_file}")

        logger.info(f"Stage 6: Successfully created combined script file: {combined_script_file}")
        return combined_script_file

    except Exception as e:
        logger.error(f"Stage 6: Error creating combined script: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def stage6_generate_script(state):
    """
    Phase 6: Generate Test Script for Selected Test Case Step.

    This stage allows the user to generate a test script for the selected test case step.
    It checks if all prerequisites are met (step selection, element matching, test data),
    displays the selected step information, and provides options for script generation.
    When the user clicks the generate button, it calls the AI to generate a test script
    and displays the result.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Phase 6: Generate Test Script</h2>", unsafe_allow_html=True)

    # Check prerequisites with specific error messages
    if not hasattr(state, 'selected_step') or not state.selected_step:
        st.warning("⚠️ Please select a test case step in Phase 4 first")
        return

    if not hasattr(state, 'step_matches') and not hasattr(state, 'element_matches'):
        st.warning("⚠️ Please complete element matching in Phase 4 first")
        return

    # Check if test data is required but not configured
    llm_step_analysis = getattr(state, 'llm_step_analysis', {})
    test_data_skipped = getattr(state, 'test_data_skipped', False)

    if llm_step_analysis.get("requires_test_data", False) and not test_data_skipped and not hasattr(state, 'test_data'):
        st.warning("⚠️ Please configure test data in Phase 5 first")
        return

    # Get the selected step information
    selected_step = state.selected_step
    selected_step_table_entry = getattr(state, 'selected_step_table_entry', None)

    # Display step information in a collapsible section
    with st.expander("Selected Step Information", expanded=True):
        col1, col2 = st.columns(2)
        with col1:
            st.markdown(f"**Step Number:** {selected_step.get('Step No')}")
            st.markdown(f"**Action:** {selected_step.get('Test Steps')}")
        with col2:
            st.markdown(f"**Expected Result:** {selected_step.get('Expected Result')}")

    # Display element matches if available
    if hasattr(state, 'element_matches') and state.element_matches:
        try:
            # Get the test case ID and step number
            test_case_id = state.selected_test_case.get('Test Case ID')
            step_no = str(selected_step.get('Step No'))

            # Get the matches for this specific step
            step_matches = state.element_matches.get(test_case_id, {}).get(step_no, [])

            if step_matches:
                with st.expander("Element Matches", expanded=False):
                    for i, match in enumerate(step_matches):
                        element = match.get('element', {})
                        element_name = element.get('name', 'Unknown')
                        selector = element.get('selector', 'Unknown')
                        action = match.get('action', 'Unknown')

                        st.markdown(f"**Match {i+1}:** {element_name} ({selector})")
                        st.markdown(f"- Action: {action}")

                        # Show confidence score if available
                        if 'score' in match:
                            score = match.get('score', 0)
                            st.progress(score)
                            st.markdown(f"- Confidence: {score:.2f}")
        except (KeyError, TypeError, AttributeError) as e:
            logger.error(f"Error displaying element matches: {str(e)}")
            st.error(f"Error displaying element matches: {str(e)}")

    # Display test data if available
    if hasattr(state, 'test_data') and state.test_data:
        with st.expander("Test Data", expanded=False):
            try:
                # Separate manual and auto-generated test data
                manual_data = {}
                auto_data = {}

                for key, value in state.test_data.items():
                    if 'manual_param' in key.lower():
                        manual_data[key] = value
                    else:
                        auto_data[key] = value

                # Display manual data first with a clear header
                if manual_data:
                    st.markdown("#### ✏️ Manually Entered Data")

                    # Create a table for better visualization
                    data = []
                    for key, value in manual_data.items():
                        # Extract step number from the key for better display
                        if 'step_' in key:
                            step_num = key.split('step_')[1]
                            # Try to determine the generic parameter based on the action
                            generic_param = "input_text"  # Default

                            # Try to find the corresponding generic parameter
                            for gen_key in auto_data.keys():
                                if gen_key in ['search_query', 'email', 'password', 'name', 'username', 'input_text']:
                                    if auto_data[gen_key] == value:
                                        generic_param = gen_key
                                        break

                            data.append({
                                "Step": f"Step {step_num}",
                                "Value": value,
                                "Used As": generic_param
                            })
                        else:
                            data.append({
                                "Step": "Unknown",
                                "Value": value,
                                "Used As": key
                            })

                    if data:
                        # Convert to DataFrame for display
                        import pandas as pd
                        df = pd.DataFrame(data)
                        st.table(df)
                    else:
                        for key, value in manual_data.items():
                            # Extract step number from the key for better display
                            if 'step_' in key:
                                step_num = key.split('step_')[1]
                                st.markdown(f"**Manual data for Step {step_num}:** `{value}`")
                            else:
                                st.markdown(f"**{key}:** `{value}`")

                    st.markdown("---")

                # Display auto-generated data
                if auto_data:
                    st.markdown("#### 🤖 Auto-Generated Data")
                    for key, value in auto_data.items():
                        st.markdown(f"**{key}:** `{value}`")
                elif not manual_data:
                    st.info("No test data available.")
            except (KeyError, TypeError, AttributeError) as e:
                logger.error(f"Error displaying test data: {str(e)}")
                st.error(f"Error displaying test data: {str(e)}")

    # Script generation section
    st.subheader("Generate Script")
    st.info("Click the button below to generate a test script for this step using Google AI.")

    # Add a button to generate the test script
    if st.button("Generate Test Script", key="generate_script_btn", use_container_width=True):
        with st.spinner("Generating test script for selected test case step..."):
                try:
                    # Import the necessary function
                    from core.ai import generate_test_script

                    # Prepare the inputs for script generation
                    test_case = state.selected_test_case
                    step = selected_step
                    step_table_entry = selected_step_table_entry

                    # Get element matches for this step
                    element_matches = []
                    if hasattr(state, 'element_matches') and state.element_matches:
                        try:
                            test_case_id = test_case.get('Test Case ID')
                            step_no = str(step.get('Step No'))
                            element_matches = state.element_matches.get(test_case_id, {}).get(step_no, [])
                            logger.info(f"Found {len(element_matches)} element matches for step {step_no}")
                        except (KeyError, TypeError) as e:
                            logger.error(f"Error retrieving element matches: {str(e)}")
                            element_matches = []

                    # Get test data for this step
                    test_data = {}
                    if hasattr(state, 'test_data') and state.test_data:
                        test_data = state.test_data
                        logger.info(f"Using test data with {len(test_data)} entries")

                        # Write test data to a file that conftest.py can read
                        try:
                            import json
                            test_data_file = "test_data_runtime.json"
                            with open(test_data_file, 'w') as f:
                                json.dump(test_data, f, indent=2)
                            logger.info(f"Wrote test data to {test_data_file} for conftest.py")
                        except Exception as e:
                            logger.warning(f"Could not write test data file: {str(e)}")

                    # Generate the test script through the centralized AI function
                    logger.info(f"Calling generate_test_script for step {step.get('Step No')} of test case {test_case.get('Test Case ID')}")

                    # Generate the test script - returns a tuple (merged_script, step_specific_script)
                    script_result = generate_test_script(
                        test_case,
                        element_matches,
                        test_data,
                        state.website_url,
                        step_table_entry,
                        state.google_api_key,
                        state  # Pass the state for script continuity
                    )

                    # Unpack the result - handle both tuple return and legacy string return
                    if isinstance(script_result, tuple):
                        merged_script, step_specific_script = script_result
                        logger.info(f"Received both merged script ({len(merged_script)} chars) and step-specific script ({len(step_specific_script)} chars)")
                    else:
                        # For backward compatibility with older versions
                        merged_script = script_result
                        step_specific_script = None
                        logger.info(f"Received only merged script ({len(merged_script)} chars) - using legacy mode")

                    # Use the merged script as the main script content
                    script_content = merged_script

                    # Check for errors before proceeding
                    if script_content and script_content.startswith("Error"):
                        st.error(script_content)
                        logger.error(f"AI returned error: {script_content}")
                    elif not script_content:
                        st.error("AI returned empty script content")
                        logger.error("AI returned empty script content")
                        return
                    else:
                        # Create file paths for both scripts with timestamp for versioning
                        script_dir = "generated_tests"
                        os.makedirs(script_dir, exist_ok=True)

                        # Create timestamp for consistent filenames
                        timestamp = int(time.time())
                        test_case_id = test_case.get('Test Case ID', 'unknown')
                        step_no = step.get('Step No', '0')

                        # Path for the merged script
                        script_file = os.path.join(
                            script_dir,
                            f"test_{test_case_id}_{step_no}_{timestamp}_merged.py"
                        )

                        # Save the merged script to a file
                        try:
                            with open(script_file, "w") as f:
                                f.write(script_content)
                            logger.info(f"Saved merged script to {script_file}")
                        except (IOError, PermissionError) as e:
                            logger.error(f"Error saving merged script: {str(e)}")
                            st.error(f"Error saving merged script: {str(e)}")
                            return

                        # If we have a step-specific script, save it too
                        step_specific_file = None
                        if step_specific_script:
                            step_specific_file = os.path.join(
                                script_dir,
                                f"test_{test_case_id}_{step_no}_{timestamp}_step_only.py"
                            )
                            try:
                                with open(step_specific_file, "w") as f:
                                    f.write(step_specific_script)
                                logger.info(f"Saved step-specific script to {step_specific_file}")
                            except (IOError, PermissionError) as e:
                                logger.error(f"Error saving step-specific script: {str(e)}")
                                st.warning(f"Error saving step-specific script: {str(e)}")
                                # Continue with the merged script only

                        # Update state with script information
                        # Track state changes with before/after values for proper logging
                        if script_content:
                            # Update last_script_content with proper logging
                            old_script_content = getattr(state, 'last_script_content', '')
                            if old_script_content != script_content:
                                state.last_script_content = script_content
                                logger.info(f"State change: last_script_content: {len(old_script_content)} chars -> {len(script_content)} chars")

                            # Update step-specific script if available
                            if step_specific_script:
                                old_step_specific_script = getattr(state, 'last_step_specific_script', '')
                                if old_step_specific_script != step_specific_script:
                                    state.last_step_specific_script = step_specific_script
                                    logger.info(f"State change: last_step_specific_script: {len(old_step_specific_script)} chars -> {len(step_specific_script)} chars")

                                if step_specific_file:
                                    old_step_specific_file = getattr(state, 'last_step_specific_file', '')
                                    if old_step_specific_file != step_specific_file:
                                        state.last_step_specific_file = step_specific_file
                                        logger.info(f"State change: last_step_specific_file: {old_step_specific_file} -> {step_specific_file}")

                            # Update script file path
                            old_script_file = getattr(state, 'last_script_file', '')
                            if old_script_file != script_file:
                                state.last_script_file = script_file
                                logger.info(f"State change: last_script_file: {old_script_file} -> {script_file}")

                            # Update generated script path
                            old_generated_script_path = getattr(state, 'generated_script_path', '')
                            if old_generated_script_path != script_file:
                                state.generated_script_path = script_file
                                logger.info(f"State change: generated_script_path: {old_generated_script_path} -> {script_file}")

                            # Update script generation flags
                            old_script_just_generated = getattr(state, 'script_just_generated', False)
                            if not old_script_just_generated:
                                state.script_just_generated = True
                                logger.info(f"State change: script_just_generated: {old_script_just_generated} -> True")

                            old_step_ready_for_script = getattr(state, 'step_ready_for_script', False)
                            if not old_step_ready_for_script:
                                state.step_ready_for_script = True
                                logger.info(f"State change: step_ready_for_script: {old_step_ready_for_script} -> True")

                            # Update script continuity tracking
                            # This ensures proper script merging in subsequent steps
                            try:
                                state.update_script_continuity(script_content, str(step_no))
                                logger.info(f"Updated script continuity tracking for step {step_no}")
                            except Exception as e:
                                logger.error(f"Error updating script continuity: {str(e)}")

                            # Check if all steps are completed and create combined script
                            try:
                                # Check if we have all steps completed for the test case
                                if (hasattr(state, 'selected_test_case') and state.selected_test_case and
                                    hasattr(state, 'previous_scripts') and state.previous_scripts):

                                    # Get total steps for the test case - try multiple sources
                                    total_steps = 0

                                    # First try: step_table_json (most reliable for automation)
                                    if hasattr(state, 'step_table_json') and state.step_table_json:
                                        total_steps = len(state.step_table_json)
                                        logger.debug(f"Stage 6: Got total_steps from step_table_json: {total_steps}")

                                    # Second try: test case Steps (fallback)
                                    elif state.selected_test_case:
                                        test_case_steps = state.selected_test_case.get('Steps', [])
                                        total_steps = len(test_case_steps)
                                        logger.debug(f"Stage 6: Got total_steps from test case Steps: {total_steps}")

                                    # Update state.total_steps if not set
                                    if hasattr(state, 'total_steps') and state.total_steps == 0 and total_steps > 0:
                                        state.total_steps = total_steps
                                        logger.info(f"State change: total_steps = {state.total_steps}")

                                    completed_steps = len(state.previous_scripts)

                                    logger.info(f"Stage 6: Script completion check - {completed_steps}/{total_steps} steps completed")

                                    # If all steps are completed, create the final step script for Stage 8 optimization
                                    if completed_steps >= total_steps and total_steps > 1:
                                        logger.info("Stage 6: All steps completed, creating final step script for Stage 8 optimization")
                                        final_script_path = create_final_step_script(state)

                                        if final_script_path:
                                            st.success(f"🎯 Final step script created for optimization: {os.path.basename(final_script_path)}")
                                            logger.info(f"Stage 6: Successfully created final step script at {final_script_path}")

                                            # Also create the traditional combined script for reference (optional)
                                            combined_script_path = create_combined_script(state)
                                            if combined_script_path:
                                                logger.info(f"Stage 6: Also created full combined script for reference at {combined_script_path}")
                                        else:
                                            logger.warning("Stage 6: Failed to create final step script")
                                    elif completed_steps >= total_steps and total_steps == 1:
                                        # For single-step test cases, just use the single step script
                                        logger.info("Stage 6: Single step test case completed, using single step script for optimization")
                                        final_script_path = create_final_step_script(state)
                                        if final_script_path:
                                            st.success(f"🎯 Single step script ready for optimization: {os.path.basename(final_script_path)}")
                                            logger.info(f"Stage 6: Successfully prepared single step script at {final_script_path}")
                                    else:
                                        logger.info(f"Stage 6: Not all steps completed yet ({completed_steps}/{total_steps}), final step script will be created when all steps are done")
                            except Exception as e:
                                logger.error(f"Stage 6: Error checking for combined script creation: {str(e)}")

                            # Display success message and script content
                            st.success(f"✅ Test script generated successfully: {os.path.basename(script_file)}")

                            # Trigger script validation after successful generation
                            if not state.script_validation_done:
                                logger.info("Starting automated script validation")
                                with st.spinner("Performing automated code quality validation..."):
                                    try:
                                        # Import the validation function
                                        from core.ai import validate_generated_script

                                        # Perform validation
                                        validation_results = validate_generated_script(
                                            script_content=script_content,
                                            test_case=state.selected_test_case,
                                            step_table_entry=selected_step_table_entry,
                                            test_data=test_data,
                                            element_matches=element_matches,
                                            api_key=state.google_api_key
                                        )

                                        # Store validation results in state
                                        state.script_validation_results = validation_results
                                        state.script_validation_done = True
                                        state.script_validation_passed = validation_results.get('ready_for_execution', False)

                                        # Add validation feedback to learning history
                                        test_case_id = state.selected_test_case.get('Test Case ID', 'Unknown')
                                        step_no = selected_step_table_entry.get('step_no', '1') if selected_step_table_entry else '1'
                                        state.add_validation_feedback(validation_results, test_case_id, step_no)

                                        logger.info(f"Script validation completed - Quality Score: {validation_results.get('quality_score', 0)}")

                                    except Exception as e:
                                        logger.error(f"Error during script validation: {e}")
                                        # Set default validation results on error
                                        state.script_validation_results = {
                                            "quality_score": 50,
                                            "syntax_valid": True,
                                            "issues_found": [{"category": "validation", "severity": "medium", "description": f"Validation error: {str(e)}"}],
                                            "recommendations": ["Review script manually"],
                                            "confidence_rating": "low",
                                            "ready_for_execution": True,
                                            "validation_error": str(e)
                                        }
                                        state.script_validation_done = True
                                        state.script_validation_passed = True  # Allow progression despite validation error

                            # Display the generated script with tabs instead of nested expanders
                            st.subheader("Generated Test Scripts")

                            # Create tabs for different script views
                            script_tabs = st.tabs([
                                "Merged Script",
                                "Step-Specific Script",
                                "Diff View",
                                "Origins View"
                            ])

                            # Tab 1: Merged Script
                            with script_tabs[0]:
                                # Add utility buttons for the script
                                col1, col2 = st.columns([1, 1])
                                with col1:
                                    if st.button("📋 Copy Script", key="copy_script"):
                                        st.session_state['clipboard_content'] = script_content
                                        st.success("✅ Script copied to clipboard! Use Ctrl+V to paste.")
                                with col2:
                                    if st.download_button(
                                        label="💾 Download Script",
                                        data=script_content,
                                        file_name=os.path.basename(script_file),
                                        mime="text/plain",
                                        key="download_script"
                                    ):
                                        st.success("✅ Script downloaded successfully!")

                                # Display the script with syntax highlighting
                                st.code(script_content, language="python")
                                st.info(f"Script file: {script_file}")

                            # Tab 2: Step-Specific Script (only if available)
                            with script_tabs[1]:
                                if step_specific_script and step_specific_file:
                                    # Add utility buttons for the step-specific script
                                    col1, col2 = st.columns([1, 1])
                                    with col1:
                                        if st.button("📋 Copy Step Script", key="copy_step_script"):
                                            st.session_state['clipboard_content'] = step_specific_script
                                            st.success("✅ Step-specific script copied to clipboard!")
                                    with col2:
                                        if st.download_button(
                                            label="💾 Download Step Script",
                                            data=step_specific_script,
                                            file_name=os.path.basename(step_specific_file),
                                            mime="text/plain",
                                            key="download_step_script"
                                        ):
                                            st.success("✅ Step-specific script downloaded successfully!")

                                    # Display the script with syntax highlighting
                                    st.code(step_specific_script, language="python")
                                    st.info(f"Step-specific script file: {step_specific_file}")
                                else:
                                    st.info("No step-specific script available for this step.")

                            # Tab 3: Diff View (only if step-specific script is available)
                            with script_tabs[2]:
                                if step_specific_script and step_specific_file:
                                    st.info("This view highlights the differences between the step-specific script and the merged script.")

                                    try:
                                        # Generate the HTML diff
                                        html_diff = generate_html_diff(step_specific_script, script_content)

                                        # Display the HTML diff
                                        st.components.v1.html(html_diff, height=600, scrolling=True)
                                    except Exception as e:
                                        logger.error(f"Error generating diff view: {str(e)}")
                                        st.error(f"Error generating diff view: {str(e)}")
                                else:
                                    st.info("Diff view requires both merged and step-specific scripts.")

                            # Tab 4: Origins View (only if step-specific script is available)
                            with script_tabs[3]:
                                if step_specific_script and step_specific_file:
                                    st.info("This view shows which parts of the merged script came from previous steps vs. the current step.")
                                    st.markdown("""
                                    <div style="margin-bottom: 10px;">
                                        <span style="background-color: #e6f3ff; padding: 2px 5px; border-radius: 3px;">■ Blue</span> - Code from current step
                                        <span style="background-color: #e6ffe6; padding: 2px 5px; border-radius: 3px; margin-left: 10px;">■ Green</span> - Code from previous steps
                                    </div>
                                    """, unsafe_allow_html=True)

                                    try:
                                        # Analyze script origins
                                        origins = analyze_script_origins(step_specific_script, script_content)

                                        # Generate color-coded HTML
                                        colored_html = generate_colored_html_script(script_content, origins)

                                        # Display the color-coded HTML
                                        st.components.v1.html(colored_html, height=600, scrolling=True)
                                    except Exception as e:
                                        logger.error(f"Error generating script origins view: {str(e)}")
                                        st.error(f"Error generating script origins view: {str(e)}")
                                else:
                                    st.info("Origins view requires both merged and step-specific scripts.")

                            # Display validation results if available
                            if state.script_validation_done and state.script_validation_results:
                                st.subheader("Code Quality Validation")

                                with st.expander("🔍 Automated Validation Results", expanded=True):
                                    validation_results = state.script_validation_results

                                    # Display quality score with color coding
                                    quality_score = validation_results.get('quality_score', 0)
                                    if quality_score >= 80:
                                        score_color = "🟢"
                                        score_status = "Excellent"
                                    elif quality_score >= 60:
                                        score_color = "🟡"
                                        score_status = "Good"
                                    else:
                                        score_color = "🔴"
                                        score_status = "Needs Improvement"

                                    col1, col2, col3 = st.columns(3)
                                    with col1:
                                        st.metric("Quality Score", f"{quality_score}/100", delta=None)
                                    with col2:
                                        st.metric("Status", f"{score_color} {score_status}")
                                    with col3:
                                        confidence = validation_results.get('confidence_rating', 'medium').title()
                                        st.metric("AI Confidence", confidence)

                                    # Display syntax validation
                                    syntax_valid = validation_results.get('syntax_valid', True)
                                    if syntax_valid:
                                        st.success("✅ Syntax validation passed")
                                    else:
                                        st.error("❌ Syntax issues detected")

                                    # Display issues found
                                    issues = validation_results.get('issues_found', [])
                                    if issues:
                                        st.subheader("Issues Found")
                                        for issue in issues:
                                            severity = issue.get('severity', 'medium')
                                            category = issue.get('category', 'general')
                                            description = issue.get('description', 'No description')

                                            if severity == 'high':
                                                st.error(f"🔴 **{category.title()}**: {description}")
                                            elif severity == 'medium':
                                                st.warning(f"🟡 **{category.title()}**: {description}")
                                            else:
                                                st.info(f"🔵 **{category.title()}**: {description}")

                                    # Display recommendations
                                    recommendations = validation_results.get('recommendations', [])
                                    if recommendations:
                                        st.subheader("Recommendations")
                                        for i, rec in enumerate(recommendations, 1):
                                            st.write(f"{i}. {rec}")

                                    # Display validation error if any
                                    if 'validation_error' in validation_results:
                                        st.warning(f"⚠️ Validation Note: {validation_results['validation_error']}")

                                # Display feedback learning status outside the main validation expander
                                if hasattr(state, 'validation_feedback_history') and state.validation_feedback_history:
                                    feedback_count = len(state.validation_feedback_history)
                                    common_issues = state.get_common_validation_issues(limit=3)

                                    with st.expander(f"📚 Learning Status ({feedback_count} validations)", expanded=False):
                                        st.info(f"The system has learned from {feedback_count} previous validations to improve script generation.")

                                        if common_issues:
                                            st.subheader("Most Common Issues Being Addressed:")
                                            for i, issue in enumerate(common_issues[:3], 1):
                                                issue_type = issue.get('type', 'issue')
                                                description = issue.get('description', '')
                                                frequency = issue.get('frequency', 1)

                                                if issue_type == 'recommendation':
                                                    st.write(f"{i}. **Improvement**: {description} (seen {frequency}x)")
                                                else:
                                                    st.write(f"{i}. **Issue**: {description} (seen {frequency}x)")
                                        else:
                                            st.write("No common patterns identified yet. Keep generating scripts to improve the learning!")
                                else:
                                    st.info("💡 **Learning Mode**: This is your first validation. The system will learn from this and future validations to improve script generation quality.")

                            # Workflow progression section with validation-aware options
                            st.subheader("Next Steps")

                            if state.script_validation_done:
                                validation_results = state.script_validation_results
                                ready_for_execution = validation_results.get('ready_for_execution', True)
                                quality_score = validation_results.get('quality_score', 50)

                                if ready_for_execution and quality_score >= 60:
                                    st.success("✅ Script validation passed. Ready to proceed to execution.")
                                elif ready_for_execution:
                                    st.warning("⚠️ Script has some issues but is ready for execution. Review recommendations above.")
                                else:
                                    st.error("❌ Script validation failed. Consider regenerating the script.")

                                # Create three columns for user options
                                col1, col2, col3 = st.columns(3)

                                with col1:
                                    if st.button("Proceed to Stage 7", use_container_width=True, type="primary" if ready_for_execution else "secondary"):
                                        logger.info("User chose to proceed to Stage 7 after validation")
                                        st.session_state['state'] = state
                                        st.session_state['stage_progression_message'] = "✅ Script validated. Proceeding to Stage 7."
                                        st.rerun()

                                with col2:
                                    if st.button("Regenerate Script", use_container_width=True):
                                        logger.info("User chose to regenerate script after validation")
                                        # Reset validation state to trigger new generation
                                        state.script_validation_done = False
                                        state.script_validation_passed = False
                                        state.script_validation_results = {}
                                        st.session_state['state'] = state
                                        st.session_state['stage_progression_message'] = "🔄 Regenerating script with validation feedback."
                                        st.rerun()

                                with col3:
                                    if st.button("Manual Review", use_container_width=True):
                                        logger.info("User chose manual review option")
                                        st.info("💡 **Manual Review Mode**: Please review the generated script and validation results above. You can proceed to Stage 7 when ready, or regenerate if needed.")
                            else:
                                # Fallback for when validation hasn't run yet
                                st.info("Review the generated script above, then proceed to run it in Stage 7.")
                                if st.button("Proceed to Run Script (Stage 7)", use_container_width=True):
                                    logger.info("User clicked 'Proceed to Run Script' button (no validation)")
                                    st.session_state['state'] = state
                                    st.session_state['stage_progression_message'] = "✅ Script generated successfully. Proceeding to Stage 7."
                                    st.rerun()
                        else:
                            if script_content and script_content.startswith("Error"):
                                # Error already displayed above
                                pass
                            else:
                                st.error("AI script generation returned empty content.")
                                logger.error("AI script generation returned empty content")
                except (KeyError, ValueError, TypeError) as e:
                    st.error(f"Error generating test script: {str(e)}")
                    logger.error(f"Error generating test script: {str(e)}", exc_info=True)
                except Exception as e:
                    st.error(f"Unexpected error generating test script: {str(e)}")
                    import traceback
                    error_details = traceback.format_exc()
                    logger.error(f"Unexpected error generating test script: {error_details}")
                    st.error(f"Error details: {error_details}")
